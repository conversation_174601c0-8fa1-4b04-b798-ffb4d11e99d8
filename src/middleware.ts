import { NextRequest, NextResponse } from 'next/server';

import { configs } from './configs';
import { permanentRedirectsService } from './services/permanentRedirects';
import { getRewriteRules } from './helpers/rewriteRules';
import { LANGUAGE_LOCALE } from './utils/constants';

const HEADER_LOCALE_NAME = 'x-locale';

export async function middleware(request: NextRequest) {
  const { pathname, origin } = request.nextUrl;
  const host = request.headers.get('host') || '';
  const locale = host?.includes(configs.host.en) ? LANGUAGE_LOCALE.EN : LANGUAGE_LOCALE.VI;
  const headers = { [HEADER_LOCALE_NAME]: locale, host };

  const redirect = await permanentRedirectsService.checkSource(pathname);
  if (redirect?.destination) {
    const url = new URL(redirect.destination, origin);
    return NextResponse.redirect(url, { headers });
  }

  const rewriteRules = getRewriteRules(locale);
  const matchedRule = rewriteRules.find((rule) => pathname.includes(rule.source));
    if (matchedRule?.destination) {
    const url = new URL(matchedRule.destination, origin);
    return NextResponse.rewrite(url, { headers });
  }

  return NextResponse.next({ headers });
}

export const config = {
  matcher: ['/((?!_next|api|favicon.ico).*)'],
};
