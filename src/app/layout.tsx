import Script from 'next/script';
import type { Metadata } from 'next';
import { ConfigProvider } from 'antd';
import AntdRegistry from '@/lib/antd.registry';
import { ToastContainer } from 'react-toastify';
import { NextIntlClientProvider } from 'next-intl';
import { getLocale, getMessages } from 'next-intl/server';
import { Manrope } from 'next/font/google';
import { isEmpty } from 'lodash-es';

import { configs } from '@/configs';
import { METADATA } from '@/constants';
import AlertApp from '@/components/Alert';
import type { Locale } from '@/utils/types';
import { APP_ROUTES } from '@/constants/url';
import { getUserLocale } from '@/services/locale';
import { theme } from '@/assets/stylesheets/theme';
import { jsonSchemasService } from '@/services/jsonSchemas';
import PromotionProvider from '@/components/PromotionProvider';
import GoogleThirdPartyAnalytics from './_components/GoogleThirdPartyAnalytics';

import 'animate.css/animate.min.css';
import 'slick-carousel/slick/slick.css';
import '@/assets/stylesheets/globals.css';
import 'slick-carousel/slick/slick-theme.css';
import 'react-toastify/dist/ReactToastify.css';
import 'react-loading-skeleton/dist/skeleton.css';


export async function generateMetadata(): Promise<Metadata> {
  const locale = await getUserLocale();
  console.log(METADATA.app[locale]);
  return METADATA.app[locale];
}

const FacebookPixelScript = () => {
  if (!configs.headers.facebookPixelId) return null;
  return (
    <Script
      id='facebook-pixel'
      dangerouslySetInnerHTML={{
        __html: `
          !function(f,b,e,v,n,t,s) {
            if(f.fbq) return;
            n=f.fbq=function(){n.callMethod?n.callMethod.apply(n,arguments):n.queue.push(arguments)};
            if(!f._fbq) f._fbq=n;
            n.push=n;
            n.loaded=!0;
            n.version='2.0';
            n.queue=[];
            t=b.createElement(e);
            t.async=!0;
            t.src=v;
            s=b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t,s);
          }(window, document,'script','https://connect.facebook.net/en_US/fbevents.js');
          fbq('init', '${configs.headers.facebookPixelId}');
          fbq('track', 'PageView');
        `,
      }}
    />
  );
};

const TikTokPixelScript = () => {
  if (!configs.headers.tiktokPixelId) return null;
  return (
    <Script
      id='tiktok-pixel'
      dangerouslySetInnerHTML={{
        __html: `
          !function (w, d, t) {
            w.TiktokAnalyticsObject=t;
            var ttq=w[t]=w[t]||[];
            ttq.methods=["page","track","identify","instances","debug","on","off","once","ready","alias","group","enableCookie","disableCookie"];
            ttq.setAndDefer=function(t,e){t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}}; 
            for(var i=0;i<ttq.methods.length;i++) ttq.setAndDefer(ttq,ttq.methods[i]);
            ttq.instance=function(t){for(var e=ttq._i[t]||[],n=0;n<ttq.methods.length;n++) ttq.setAndDefer(e,ttq.methods[n]);return e};
            ttq.load=function(e,n){
              var i="https://analytics.tiktok.com/i18n/pixel/events.js";
              ttq._i=ttq._i||{}, ttq._i[e]=[], ttq._i[e]._u=i, ttq._t=ttq._t||{}, ttq._t[e]=+new Date, ttq._o=ttq._o||{}, ttq._o[e]=n||{};
              var o=document.createElement("script");
              o.type="text/javascript",o.async=!0,o.src=i+"?sdkid="+e+"&lib="+t;
              var a=document.getElementsByTagName("script")[0];
              a.parentNode.insertBefore(o,a);
            };
            ttq.load('${configs.headers.tiktokPixelId}');
            ttq.page();
          }(window, document, 'ttq');
        `,
      }}
    />
  );
};

const GoogleAdsScript = () => {
  const googleAdsClientId = process.env.NEXT_PUBLIC_GOOGLE_ADS_CLIENT_ID;
  if (!googleAdsClientId) return null;
  return (
    <Script
      id='google-ads'
      async
      src={`https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=${googleAdsClientId}`}
      crossOrigin='anonymous'
    />
  );
};

export default async function RootLayout({ children }: React.PropsWithChildren) {
  const locale = await getLocale();
  const messages = await getMessages();
  const { schema } = await jsonSchemasService.getByPage(APP_ROUTES.home);

  return (
    <html lang={locale} >
      <head>
        <GoogleAdsScript />
        <TikTokPixelScript />
        <FacebookPixelScript />
        {!isEmpty(schema) && (
          <Script
            id='schema-home'
            type='application/ld+json'
            dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
          />
        )}
        {locale === 'en' && <meta name='facebook-domain-verification' content='slrqwvfsar183ujq329rhdmb6u4tvk' />}
      </head>
      <body>
        <GoogleThirdPartyAnalytics locale={locale as Locale} />
        <ToastContainer position='top-right' autoClose={2000} />
        <AntdRegistry>
          <ConfigProvider theme={theme}>
            <NextIntlClientProvider messages={messages}>
              <PromotionProvider>{children}</PromotionProvider>
            </NextIntlClientProvider>
          </ConfigProvider>
          <AlertApp />
        </AntdRegistry>
      </body>
    </html>
  );
}
